#!/usr/bin/env python3
"""
Script to generate word definitions for C-starting markdown files
"""

import os
import glob

# Dictionary of word definitions for C-starting words
word_definitions = {
    "Cafe": {
        "pos": "名词 (noun)",
        "phonetic": "英 /ˈkæfeɪ/ 美 /kæˈfeɪ/",
        "meaning_cn": "咖啡馆，小餐厅",
        "meaning_en": "a small restaurant where you can buy drinks and simple meals",
        "etymology": "源自法语 \"café\"，来自土耳其语 \"kahve\"，意为咖啡。",
        "synonyms": "restaurant, bistro, coffeehouse, eatery",
        "antonyms": "无明显反义词",
        "collocations": ["internet cafe (网吧)", "street cafe (街边咖啡馆)", "outdoor cafe (露天咖啡馆)"],
        "examples": [
            "We met at a cozy cafe downtown. (我们在市中心一家舒适的咖啡馆见面。)",
            "The cafe serves excellent coffee and pastries. (这家咖啡馆供应优质的咖啡和糕点。)"
        ]
    },
    "Cage": {
        "pos": "名词 (noun), 动词 (verb)",
        "phonetic": "英 /keɪdʒ/ 美 /keɪdʒ/",
        "meaning_cn": "笼子；关在笼中",
        "meaning_en": "a structure made of metal bars or wire in which animals or birds can be kept",
        "etymology": "源自古法语 \"cage\"，来自拉丁语 \"cavea\"，意为 \"enclosure\"。",
        "synonyms": "enclosure, pen, coop, confine, imprison",
        "antonyms": "free, release, liberate",
        "collocations": ["bird cage (鸟笼)", "cage fight (笼斗)", "cage door (笼门)"],
        "examples": [
            "The bird was kept in a golden cage. (鸟被关在金笼子里。)",
            "They decided to cage the wild animal for safety. (为了安全，他们决定把野生动物关在笼子里。)"
        ]
    },
    "Cake": {
        "pos": "名词 (noun), 动词 (verb)",
        "phonetic": "英 /keɪk/ 美 /keɪk/",
        "meaning_cn": "蛋糕；结块",
        "meaning_en": "a sweet food made from a mixture of flour, eggs, fat, and sugar",
        "etymology": "源自古北欧语 \"kaka\"，意为 \"cake\"。",
        "synonyms": "pastry, dessert, confection, harden, solidify",
        "antonyms": "无明显反义词",
        "collocations": ["birthday cake (生日蛋糕)", "wedding cake (婚礼蛋糕)", "piece of cake (小菜一碟)"],
        "examples": [
            "She baked a chocolate cake for the party. (她为聚会烤了一个巧克力蛋糕。)",
            "The mud caked on his boots. (泥土在他的靴子上结块了。)"
        ]
    }
}

def generate_word_content(word, definition):
    """Generate markdown content for a word definition"""
    content = f"""# {word}

**词性：** {definition['pos']}

**音标：** {definition['phonetic']}

**发音链接：** [点击听发音](https://forvo.com/word/{word.lower()}/)

## 中文含义与解释
**n. (名词):** 
- 中文释义: {definition['meaning_cn']}
- 英文解释 (Cambridge): "{definition['meaning_en']}"

## 词源（Etymology）
{definition['etymology']}

## 同义词与反义词
**名词 (noun):**
- **近义词 (Synonyms):** {definition['synonyms']}
- **反义词 (Antonyms):** {definition['antonyms']}

## 常见搭配（Collocations）
**名词 (noun):**
"""
    
    for collocation in definition['collocations']:
        content += f"- {collocation}\n"
    
    content += "\n## 实用例句\n"
    
    for i, example in enumerate(definition['examples'], 1):
        content += f"{i}. **{example}**\n"
    
    return content

def main():
    # Find all C-starting markdown files
    c_files = glob.glob("C*.md")
    
    print(f"Found {len(c_files)} files starting with C")
    
    # Process first few files as examples
    for filename in c_files[:3]:  # Process first 3 files
        word = filename[:-3]  # Remove .md extension
        
        if word in word_definitions:
            print(f"Processing {filename}...")
            
            # Check if file is empty or has minimal content
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                
                if len(content) <= 1:  # Empty or just whitespace
                    # Generate new content
                    new_content = generate_word_content(word, word_definitions[word])
                    
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(new_content)
                    
                    print(f"Generated content for {filename}")
                else:
                    print(f"Skipping {filename} - already has content")
                    
            except Exception as e:
                print(f"Error processing {filename}: {e}")

if __name__ == "__main__":
    main()
