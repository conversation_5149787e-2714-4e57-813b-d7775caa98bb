#!/usr/bin/env python3
"""
Batch script to generate word definitions for remaining C-starting markdown files
"""

import os
import glob

# Template for generating word definitions
def generate_basic_template(word):
    """Generate a basic template for words without specific definitions"""
    word_lower = word.lower()
    
    # Basic template with placeholders
    template = f"""# {word}

**词性：** 名词 (noun)

**音标：** 英 /.../ 美 /.../

**发音链接：** [点击听发音](https://forvo.com/word/{word_lower}/)

## 中文含义与解释
**n. (名词):** 
- 中文释义: [待补充]
- 英文解释 (Cambridge): "[待补充]"

## 词源（Etymology）
[待补充词源信息]

## 同义词与反义词
**名词 (noun):**
- **近义词 (Synonyms):** [待补充]
- **反义词 (Antonyms):** [待补充]

## 常见搭配（Collocations）
**名词 (noun):**
- [待补充搭配]

## 实用例句
1. **[待补充例句]**
   （[待补充中文翻译]）
2. **[待补充例句]**
   （[待补充中文翻译]）
"""
    return template

def main():
    # Find all C-starting markdown files
    c_files = glob.glob("C*.md")
    
    processed_count = 0
    skipped_count = 0
    
    print(f"Found {len(c_files)} files starting with C")
    
    for filename in c_files:
        word = filename[:-3]  # Remove .md extension
        
        try:
            # Check if file is empty or has minimal content
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            if len(content) <= 1:  # Empty or just whitespace
                print(f"Processing {filename}...")
                
                # Generate basic template
                new_content = generate_basic_template(word)
                
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                processed_count += 1
                print(f"Generated template for {filename}")
            else:
                skipped_count += 1
                print(f"Skipping {filename} - already has content")
                
        except Exception as e:
            print(f"Error processing {filename}: {e}")
    
    print(f"\nSummary:")
    print(f"Processed: {processed_count} files")
    print(f"Skipped: {skipped_count} files")
    print(f"Total: {len(c_files)} files")

if __name__ == "__main__":
    main()
